import type { RouteRecordStringComponent } from '@vben/types';

import { $t } from '@vben/locales';

// const {
//   version,
//   // vite inject-metadata 插件注入的全局变量
// } = __VBEN_ADMIN_METADATA__ || {};

/**
 * 该文件放非后台返回的路由 比如个人中心 等需要跳转显示的页面
 */
const localRoutes: RouteRecordStringComponent[] = [
  {
    component: '/_core/profile/index',
    meta: {
      icon: 'mingcute:profile-line',
      title: $t('ui.widgets.profile'),
      hideInMenu: true,
      requireHomeRedirect: true,
    },
    name: 'Profile',
    path: '/profile',
  },
  {
    component: '/system/oss-config/index',
    meta: {
      activePath: '/system/oss',
      icon: 'ant-design:setting-outlined',
      title: 'oss配置',
      hideInMenu: true,
      requireHomeRedirect: true,
    },
    name: 'OssConfig',
    path: '/system/oss-config',
  },
  {
    component: '/views/tool/gen/edit-gen',
    meta: {
      activePath: '/tool/gen',
      icon: 'tabler:code',
      title: '生成配置',
      hideInMenu: true,
      requireHomeRedirect: true,
    },
    name: 'GenConfig',
    path: '/code-gen/edit/:tableId',
  },
  {
    component: '/system/role-assign/index',
    meta: {
      activePath: '/system/role',
      icon: 'eos-icons:role-binding-outlined',
      title: '分配角色',
      hideInMenu: true,
      requireHomeRedirect: true,
    },
    name: 'RoleAssign',
    path: '/system/role-assign/:roleId',
  },
  {
    component: '/workflow/components/flow-designer',
    meta: {
      activePath: '/workflow/processDefinition',
      icon: 'fluent-mdl2:flow',
      title: '流程设计',
      hideInMenu: true,
      requireHomeRedirect: true,
    },
    name: 'WorkflowDesigner',
    path: '/workflow/designer',
  },
  /**
   * 需要添加iframe路由 同目录的./workflow-iframe.ts
   */
  {
    component: 'workflow/leave/leave-form',
    meta: {
      icon: 'flat-color-icons:leave',
      title: '请假申请',
      activePath: '/demo/leave',
      hideInMenu: true,
      requireHomeRedirect: true,
    },
    name: 'WorkflowLeaveIndex',
    path: '/workflow/leaveEdit/index',
  },
];

/**
 * 这里放本地路由
 */
export const localMenuList: RouteRecordStringComponent[] = [
  {
    component: 'BasicLayout',
    meta: {
      order: -1,
      title: 'page.dashboard.title',
      // 不使用基础布局（仅在顶级生效）
      noBasicLayout: true,
    },
    name: 'Dashboard',
    path: '/',
    redirect: '/analytics',
    children: [
      {
        name: 'Analytics',
        path: '/analytics',
        component: '/dashboard/analytics/index',
        meta: {
          affixTab: true,
          icon: 'lucide:building-2',
          title: '公司财务状态',
        },
      },


      // {
      //   name: 'VoucherOverview2',
      //   path: '/voucher-overview2',
      //   component: '/dashboard/voucher-overview2/index',
      //   meta: {
      //     icon: 'lucide:table',
      //     title: 'page.dashboard.voucherOverview2',
      //   },
      // },
      // {
      //   name: 'V5UpdateLog',
      //   path: '/changelog',
      //   component: '/演示使用自行删除/changelog/index',
      //   meta: {
      //     icon: 'lucide:book-open-text',
      //     keepAlive: true,
      //     title: '更新记录',
      //     badge: `当前: ${version}`,
      //     badgeVariants: 'bg-primary',
      //   },
      // },
    ],
  },

  {
    component: 'BasicLayout',
    meta: {
      icon: 'icon-park-solid:permissions',
      order: 3,
      title: '记账凭证',
    },
    name: 'bookkeeping',
    path: '/bookkeeping',
    children: [
      {
        name: 'VoucherOverview',
        path: '/voucher-overview',
        component: '/dashboard/voucher-overview/index',
        meta: {
          icon: 'lucide:file-text',
          title: 'AI凭证',
        },
      },
      {
        name: 'bookkeeping-enter',
        path: '/bookkeeping/enter',
        component: '/account-book/bookkeeping/enter/index',
        meta: {
          icon: 'ant-design:home-outlined',
          title: '录入凭证',
        },
      },
      {
        name: 'bookkeeping-view',
        path: '/bookkeeping/view',
        component: '/account-book/bookkeeping/view/index',
        meta: {
          icon: 'ant-design:home-outlined',
          title: '查看凭证',
        },
      },
    ],
  },
  {
    component: 'BasicLayout',
    meta: {
      icon: 'lucide:file-text',
      order: 2,
      title: '原始凭证',
    },
    name: 'original-voucher',
    path: '/original-voucher',
    children: [
      {
        name: 'original-voucher-expenses',
        path: '/original-voucher/expenses',
        component: '/default/index',
        meta: {
          icon: 'lucide:book-open-text',
          title: '费用发票',
        },
      },
      {
        name: 'original-voucher-purchase',
        path: '/original-voucher/purchase',
        component: '/default/index',
        meta: {
          icon: 'mdi:github',
          title: '采购和销售发票',
        },
      },
      {
        name: 'original-voucher-rule',
        path: '/original-voucher/rule',
        component: '/default/index',
        meta: {
          icon: 'mdi:github',
          title: '凭证规则设置',
        },
      },
    ],
  },
  ...localRoutes,
];
