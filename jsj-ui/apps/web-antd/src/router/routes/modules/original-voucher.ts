import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:file-text',
      order: 2,
      title: '原始凭证',
    },
    name: 'original-voucher',
    path: '/original-voucher',
    children: [
      {
        name: 'original-voucher-expenses',
        path: '/original-voucher/expenses',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'lucide:book-open-text',
          title: '费用发票',
        },
      },
      {
        name: 'original-voucher-purchase',
        path: '/original-voucher/purchase',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'mdi:github',
          title: '采购和销售发票',
        },
      },
      {
        name: 'original-voucher-rule',
        path: '/original-voucher/rule',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'mdi:github',
          title: '凭证规则设置',
        },
      },
      {
        name: 'original-voucher-obtain',
        path: '/original-voucher/obtain',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'ant-design:home-outlined',
          title: '获取原始凭证',
        },
      },
      {
        name: 'original-voucher-bank',
        path: '/original-voucher/bank',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'ant-design:home-outlined',
          title: '银行',
        },
      },
      {
        name: 'original-voucher-wages',
        path: '/original-voucher/wages',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'ant-design:home-outlined',
          title: '工资',
        },
      },
    ],
  },
];

export default routes;
