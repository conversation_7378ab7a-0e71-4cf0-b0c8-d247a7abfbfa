import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:layout-dashboard',
      order: -1,
      title: $t('page.dashboard.title'),
    },
    name: 'Dashboard',
    path: '/dashboard',
    children: [
      {
        name: 'Analytics',
        path: '/analytics',
        component: () => import('#/views/dashboard/analytics/index.vue'),
        meta: {
          affixTab: true,
          icon: 'lucide:area-chart',
          title: $t('page.dashboard.analytics'),
        },
      },


      // {
      //   name: 'VoucherOverview2',
      //   path: '/voucher-overview2',
      //   component: () => import('#/views/jsj/voucher-overview2/index.vue'),
      //   meta: {
      //     icon: 'lucide:table',
      //     title: $t('page.dashboard.voucherOverview2'),
      //   },
      // },
    ],
  },
];

export default routes;
