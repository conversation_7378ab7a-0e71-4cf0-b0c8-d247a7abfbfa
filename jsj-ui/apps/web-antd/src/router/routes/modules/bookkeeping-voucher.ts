import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'icon-park-solid:permissions',
      order: 3,
      title: '记账凭证',
    },
    name: 'bookkeeping',
    path: '/bookkeeping',
    children: [
      {
        name: 'bookkeeping-enter',
        path: '/bookkeeping/enter',
        component: () => import('#/views/account-book/bookkeeping/enter/index.vue'),
        meta: {
          icon: 'ant-design:home-outlined',
          title: '录入凭证',
        },
      },
      {
        name: 'bookkeeping-view',
        path: '/bookkeeping/view',
        component: () => import('#/views/account-book/bookkeeping/view/index.vue'),
        meta: {
          icon: 'ant-design:home-outlined',
          title: '查看凭证',
        },
      },
    ],
  },
];

export default routes;
