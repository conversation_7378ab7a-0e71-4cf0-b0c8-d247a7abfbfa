import { defineStore } from 'pinia';
import { ref } from 'vue';

// 公司信息接口
export interface CompanyInfo {
  id: string;
  name: string;
  code: string;
  taxNumber?: string;
  address?: string;
  phone?: string;
  bankAccount?: string;
  legalPerson?: string;
}

// 账套信息接口
export interface AccountBookInfo {
  id: string;
  name: string;
  companyId: string;
  startDate: string;
  endDate: string;
  currency: string;
  isDefault: boolean;
}

export const useCurrentCustomerStore = defineStore('currentCustomer', () => {
  // 当前选中的公司信息
  const currentCompany = ref<CompanyInfo | null>(null);
  
  // 当前选中的账套信息
  const currentAccountBook = ref<AccountBookInfo | null>(null);
  
  // 公司列表
  const companyList = ref<CompanyInfo[]>([]);
  
  // 账套列表
  const accountBookList = ref<AccountBookInfo[]>([]);

  // 设置当前公司
  const setCurrentCompany = (company: CompanyInfo | null) => {
    currentCompany.value = company;
  };

  // 设置当前账套
  const setCurrentAccountBook = (accountBook: AccountBookInfo | null) => {
    currentAccountBook.value = accountBook;
  };

  // 设置公司列表
  const setCompanyList = (list: CompanyInfo[]) => {
    companyList.value = list;
  };

  // 设置账套列表
  const setAccountBookList = (list: AccountBookInfo[]) => {
    accountBookList.value = list;
  };

  // 获取公司信息
  const fetchCompanyList = async () => {
    // 模拟API调用
    const mockCompanies: CompanyInfo[] = [
      {
        id: '1',
        name: '示例科技有限公司',
        code: 'DEMO001',
        taxNumber: '91110000000000000X',
        address: '北京市朝阳区示例大厦',
        phone: '010-********',
        bankAccount: '********90********9',
        legalPerson: '张三',
      },
      {
        id: '2',
        name: '测试贸易公司',
        code: 'TEST002',
        taxNumber: '91110000000000001Y',
        address: '上海市浦东新区测试路123号',
        phone: '021-********',
        bankAccount: '9********09********',
        legalPerson: '李四',
      },
    ];
    
    setCompanyList(mockCompanies);
    
    // 如果没有当前公司，设置第一个为默认
    if (!currentCompany.value && mockCompanies.length > 0) {
      setCurrentCompany(mockCompanies[0]);
    }
  };

  // 获取账套信息
  const fetchAccountBookList = async (companyId?: string) => {
    // 模拟API调用
    const mockAccountBooks: AccountBookInfo[] = [
      {
        id: '1',
        name: '2024年度账套',
        companyId: companyId || '1',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        currency: 'CNY',
        isDefault: true,
      },
      {
        id: '2',
        name: '2023年度账套',
        companyId: companyId || '1',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        currency: 'CNY',
        isDefault: false,
      },
    ];
    
    setAccountBookList(mockAccountBooks);
    
    // 如果没有当前账套，设置默认账套
    if (!currentAccountBook.value) {
      const defaultBook = mockAccountBooks.find(book => book.isDefault);
      if (defaultBook) {
        setCurrentAccountBook(defaultBook);
      } else if (mockAccountBooks.length > 0) {
        setCurrentAccountBook(mockAccountBooks[0]);
      }
    }
  };

  // 初始化数据
  const initializeData = async () => {
    await fetchCompanyList();
    if (currentCompany.value) {
      await fetchAccountBookList(currentCompany.value.id);
    }
  };

  return {
    // 状态
    currentCompany,
    currentAccountBook,
    companyList,
    accountBookList,
    
    // 方法
    setCurrentCompany,
    setCurrentAccountBook,
    setCompanyList,
    setAccountBookList,
    fetchCompanyList,
    fetchAccountBookList,
    initializeData,
  };
});
