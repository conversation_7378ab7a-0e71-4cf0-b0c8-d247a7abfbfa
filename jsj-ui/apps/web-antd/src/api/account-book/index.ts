// 账套相关API模拟

export interface ApiResponse<T = any> {
  returnCode: string;
  returnMsg: string;
  data: T;
}

export const getAssetType = async (): Promise<ApiResponse<any[]>> => {
  return {
    returnCode: '200',
    returnMsg: '成功',
    data: [
      { id: '1', name: '固定资产' },
      { id: '2', name: '流动资产' },
    ],
  };
};

export const getForeignCurrencyType = async (): Promise<ApiResponse<any[]>> => {
  return {
    returnCode: '200',
    returnMsg: '成功',
    data: [
      { id: '1', name: '人民币', code: 'CNY' },
      { id: '2', name: '美元', code: 'USD' },
    ],
  };
};

export const addForeignSave = async (data: any): Promise<ApiResponse<any>> => {
  return {
    returnCode: '200',
    returnMsg: '保存成功',
    data: { id: Date.now().toString(), ...data },
  };
};
