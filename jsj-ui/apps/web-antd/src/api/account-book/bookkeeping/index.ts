// 模拟API接口，用于记账凭证功能
import { message } from 'ant-design-vue';

// 类型定义
export interface ApiResponse<T = any> {
  returnCode: string;
  returnMsg: string;
  data: T;
}

export interface SummaryItm {
  id: string;
  text: string;
}

export type SummaryItmData = SummaryItm[];

export interface LedgerItm {
  id: string;
  name: string;
  code: string;
  text: string;
  fullName: string;
}

export interface AuxiliaryItm {
  id: string;
  name: string;
}

export interface VoucherSaveDataItm {
  credit?: string;
  debit?: string;
  subjectId: string;
  summary: string;
}

export interface VoucherSaveData {
  attachmentCount: number;
  credit: string;
  dateTime: number;
  debit: string;
  detail: VoucherSaveDataItm[];
  insertMode: boolean;
  insertNumber: any;
  insertWord: any;
  voucherNo: number;
  voucherNumber: number;
  voucherType: 'NORMAL';
  voucherWord: string;
}

export interface VoucherTemplate {
  attachmentCount: number;
  detail: VoucherSaveDataItm[];
  id?: string;
  isAmount: boolean;
  name: string;
}

// 模拟API函数
export const getSummary = async (): Promise<ApiResponse<SummaryItm[]>> => {
  // 模拟摘要数据
  return {
    returnCode: '200',
    returnMsg: '成功',
    data: [
      { id: '1', text: '提现' },
      { id: '2', text: '存现' },
      { id: '3', text: '转账' },
      { id: '4', text: '收款' },
      { id: '5', text: '付款' },
    ],
  };
};

export const getLedger = async (): Promise<ApiResponse<LedgerItm[]>> => {
  // 模拟科目数据
  return {
    returnCode: '200',
    returnMsg: '成功',
    data: [
      { id: '1001', name: '库存现金', code: '1001', text: '库存现金', fullName: '资产-库存现金' },
      { id: '1002', name: '银行存款', code: '1002', text: '银行存款', fullName: '资产-银行存款' },
      { id: '1122', name: '应收账款', code: '1122', text: '应收账款', fullName: '资产-应收账款' },
      { id: '2202', name: '应付账款', code: '2202', text: '应付账款', fullName: '负债-应付账款' },
      { id: '6001', name: '主营业务成本', code: '6001', text: '主营业务成本', fullName: '成本-主营业务成本' },
    ],
  };
};

export const getAuxiliary = async (): Promise<ApiResponse<AuxiliaryItm[]>> => {
  // 模拟辅助核算数据
  return {
    returnCode: '200',
    returnMsg: '成功',
    data: [
      { id: '1', name: '供应商A' },
      { id: '2', name: '客户B' },
      { id: '3', name: '员工C' },
    ],
  };
};

export const getVoucherNo = async (year: number, month: number, voucherWord: string): Promise<ApiResponse<string>> => {
  // 模拟获取凭证编号
  const randomNo = Math.floor(Math.random() * 100) + 1;
  return {
    returnCode: '200',
    returnMsg: '成功',
    data: randomNo.toString(),
  };
};

export const setVoucherSave = async (data: VoucherSaveData): Promise<ApiResponse<any>> => {
  // 模拟保存凭证
  console.log('保存凭证数据:', data);
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  return {
    returnCode: '200',
    returnMsg: '保存成功',
    data: {
      id: Date.now().toString(),
      ...data,
    },
  };
};

export const addVoucherTemplate = async (data: VoucherTemplate): Promise<ApiResponse<any>> => {
  // 模拟保存模板
  console.log('保存模板数据:', data);
  
  return {
    returnCode: '200',
    returnMsg: '模板保存成功',
    data: {
      id: Date.now().toString(),
      ...data,
    },
  };
};

export const getVoucherList = async (data: any, page: number, sortType: string): Promise<ApiResponse<any>> => {
  // 模拟获取凭证列表
  return {
    returnCode: '200',
    returnMsg: '成功',
    data: {
      list: [],
      total: 0,
    },
  };
};

export const changeVoucherNumber = async (id: string, voucherNo: string): Promise<ApiResponse<any>> => {
  // 模拟修改凭证编号
  console.log('修改凭证编号:', { id, voucherNo });

  return {
    returnCode: '200',
    returnMsg: '修改成功',
    data: {
      id,
      voucherNo,
    },
  };
};

export const getServiceTypeSearch = async (): Promise<ApiResponse<any[]>> => {
  // 模拟获取业务类型列表
  return {
    returnCode: '200',
    returnMsg: '成功',
    data: [
      {
        id: '1',
        name: '销售收入',
        creditSubject: '6001',
        debitSubject: '1001',
        taxSubject: '2221',
      },
      {
        id: '2',
        name: '采购支出',
        creditSubject: '1001',
        debitSubject: '5001',
        taxSubject: '2222',
      },
    ],
  };
};

export const getServiceTypeSubjectId = async (subjectCode: string): Promise<ApiResponse<any>> => {
  // 模拟根据科目编码获取科目ID
  return {
    returnCode: '200',
    returnMsg: '成功',
    data: {
      id: subjectCode,
      code: subjectCode,
    },
  };
};

export const generatorBusinesstype = async (): Promise<ApiResponse<any>> => {
  // 模拟预置业务类型
  return {
    returnCode: '200',
    returnMsg: '预置业务类型成功',
    data: {},
  };
};

export const addServiceType = async (data: any): Promise<ApiResponse<any>> => {
  // 模拟添加业务类型
  console.log('添加业务类型:', data);

  return {
    returnCode: '200',
    returnMsg: '添加业务类型成功',
    data: {
      id: Date.now().toString(),
      ...data,
    },
  };
};

export const getServiceTypeList = async (): Promise<ApiResponse<any[]>> => {
  // 模拟获取业务类型列表
  return {
    returnCode: '200',
    returnMsg: '成功',
    data: [
      {
        id: '1',
        name: '销售收入',
        creditSubject: '6001',
        debitSubject: '1001',
        taxSubject: '2221',
      },
      {
        id: '2',
        name: '采购支出',
        creditSubject: '1001',
        debitSubject: '5001',
        taxSubject: '2222',
      },
    ],
  };
};

export const getDeleteServiceType = async (id: string): Promise<ApiResponse<any>> => {
  // 模拟删除业务类型
  console.log('删除业务类型:', id);

  return {
    returnCode: '200',
    returnMsg: '删除业务类型成功',
    data: {},
  };
};

export const getSubjectCode = async (parentId: string): Promise<ApiResponse<any>> => {
  // 模拟获取科目编码
  return {
    returnCode: '200',
    returnMsg: '成功',
    data: {
      code: `${parentId}01`,
    },
  };
};

export const getSubjectInfo = async (id: string): Promise<ApiResponse<any>> => {
  // 模拟获取科目详情
  return {
    returnCode: '200',
    returnMsg: '成功',
    data: {
      id,
      name: '示例科目',
      code: '1001',
      parentId: '1000',
    },
  };
};

export const addSubjectSave = async (data: any): Promise<ApiResponse<any>> => {
  // 模拟保存科目
  console.log('保存科目:', data);

  return {
    returnCode: '200',
    returnMsg: '保存科目成功',
    data: {
      id: Date.now().toString(),
      ...data,
    },
  };
};

export const saveSummary = async (data: any): Promise<ApiResponse<any>> => {
  // 模拟保存摘要
  console.log('保存摘要:', data);

  return {
    returnCode: '200',
    returnMsg: '保存摘要成功',
    data: {
      id: Date.now().toString(),
      ...data,
    },
  };
};
