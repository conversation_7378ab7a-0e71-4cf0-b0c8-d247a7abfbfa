// 公共数据配置
export const commondata = {
  // 凭证字类型
  bookTypes: [
    { label: '记', value: '记' },
    { label: '收', value: '收' },
    { label: '付', value: '付' },
    { label: '转', value: '转' },
  ],

  // 凭证类型
  voucherTypes: [
    { label: '普通凭证', value: 'NORMAL' },
    { label: '期初余额', value: 'INITIAL' },
    { label: '结转凭证', value: 'CARRY_FORWARD' },
  ],

  // 审核状态
  auditStatus: [
    { label: '未审核', value: 'NONE' },
    { label: '已审核', value: 'AUDITED' },
  ],

  // 辅助核算类型
  auxiliaryAccounting: [
    { label: '全部', value: 'All' },
    { label: '供应商', value: 'Supplier' },
    { label: '客户', value: 'Customer' },
    { label: '员工', value: 'Employee' },
    { label: '部门', value: 'Department' },
  ],

  // 凭证来源类型
  voucherType: [
    { label: '全部', value: 'All' },
    { label: '普通凭证', value: 'NORMAL' },
    { label: '银行回单', value: 'BANK' },
    { label: '发票', value: 'INVOICE' },
  ],
};
