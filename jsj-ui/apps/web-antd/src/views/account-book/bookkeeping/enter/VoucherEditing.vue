<script setup lang="ts">
import type { ListItm } from '../../index.d.ts';

import { defineComponent, defineEmits, defineProps, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue';

import { useVbenModal, VbenButton } from '@vben/common-ui';

import { MinusOutlined, PlusOutlined } from '@ant-design/icons-vue';

import { useAbstractData, useSubjectData } from '#/hooks/account-book/voucher/index';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import { uTconvert, uTcurrencyToUpperCase } from '#/utils/index';

import AuxiliarySelect from '../../components/AuxiliarySelect/index.vue';
import UserSelectBox from '../components/UserSelectBox.vue';
import AddSubjectPop from './AddSubjectPop.vue';
import AddSummaryPop from './AddSummaryPop.vue';
import Ulvalue from './Ulvalue.vue';
import emitter from './usermitt';

const useCustomer = useCurrentCustomerStore();
// const emits = defineEmits(['changeSelectValue', 'changeInputValue', 'addOrDeleteTable', 'foreignChange']);
let oldvoucherdata: null | string = localStorage.getItem(`${import.meta.env.VITE_APP_NAMESPACE}-current-voucher`);
const oldvoucherdata_customer_id: null | string = localStorage.getItem(`${import.meta.env.VITE_APP_NAMESPACE}-current-voucher-customer_id`);
// 此处需要对公司进行判断如果是切换公司了 这里的存储要清空下
// useCustomer.customerId
if (oldvoucherdata) {
    if (oldvoucherdata_customer_id == useCustomer.customerId) {
        oldvoucherdata = JSON.parse(oldvoucherdata as string);
    } else {
        oldvoucherdata = null;
        localStorage.setItem(`${import.meta.env.VITE_APP_NAMESPACE}-current-voucher`, '');
    }
}
// const { borrowerAll, lenderAll, list, isCurrency, amountTo } = defineProps<{
//     amountTo: string;
//     borrowerAll: string;
//     isCurrency: boolean;
//     lenderAll: string;
//     list: ListItm[];
// }>();

const state = reactive<{
    amountTo: string;
    borrowerAll: string;
    isCurrency: boolean;
    lenderAll: string;
    list: ListItm[];
}>({
    amountTo: '',
    borrowerAll: '',
    lenderAll: '',
    isCurrency: false, // 是否有外币这一列
    list: oldvoucherdata || [
        // 默认有四组数据
        {
            borrower: '',
            lender: '',
            balance: 0,
        },
        {
            borrower: '',
            lender: '',
            balance: 0,
        },
        {
            borrower: '',
            lender: '',
            balance: 0,
        },
        {
            borrower: '',
            lender: '',
            balance: 0,
        },
    ],
});

const [SummaryModal, SummarymodalApi] = useVbenModal({
    // 连接抽离的组件
    connectedComponent: AddSummaryPop,
});
const [SubjectModal, SubjectmodalApi] = useVbenModal({
    // 连接抽离的组件
    connectedComponent: AddSubjectPop,
});
const outDomRef = ref<any>(null);
type TargetXY = {
    h: number;
    w: number;
    x: number;
    y: number;
};
// 摘要
const abstractCoor = ref<TargetXY | undefined>(); // 摘要选择位置坐标
const subjectCoor = ref<TargetXY | undefined>(); // 科目选择位置坐标
const inputCoor = ref<TargetXY | undefined>(); // 金额输入框坐标
const currencyCoor = ref<TargetXY | undefined>(); // 汇率输入位置坐标
const foreignMoney = ref<string>(''); // 外币金额
const foreignRate = ref<string>(''); // 外币汇率
const foreignType = ref<string>(''); // 外币种类
const inputValue = ref<string>('');
const inputRef = ref<any>();
// 摘要数据
const useAbstract = useAbstractData();
const useSubject = useSubjectData();
const selectshow = () => {
    abstractCoor.value = undefined;
    subjectCoor.value = undefined;
    inputCoor.value = undefined;
    currencyCoor.value = undefined;
};
let currtIndex = 0; // 当前输入行
let inputType = ''; // input输入框的类型 用来区分是借方还是贷方

// 数据验证
const data_validation = (type?: string) => {
    let isOk = true; // 是否验证通过
    let currindex = 0; // 验证到第几行
    let tips = '';
    // 这个表默认有4行 最少输入2行
    while (state.list[currindex]) {
        let curr_is_ok = true;
        const item = state.list[currindex];
        if (item) {
            console.log(item, currindex);
            if (item.abstract || item.subject || item.borrower || item.lender) {
                console.log('a1', currindex);
                if (!item.abstract) {
                    tips += '摘要不能为空，';
                    curr_is_ok = false;
                }
                if (!item.subject) {
                    tips += '科目不能为空，';
                    curr_is_ok = false;
                }
                if (type === 'template') {
                    // 模板的时候不判断金额
                } else {
                    // 保存模板的时候不验证填写的金额
                    if (!item.borrower && !item.lender) {
                        curr_is_ok = false;
                        tips += '借方和贷方金额不能同时为空，';
                    }
                }
            } else {
                if (currindex === 0) {
                    curr_is_ok = false;
                    tips += '请完成数据填写';
                }
            }
        }
        if (curr_is_ok) {
            currindex++;
        } else {
            isOk = false;
            break;
        }
    }
    if (isOk) {
        // 每一行的都通过了 检查金额是否平衡
        if (state.borrowerAll != state.lenderAll) {
            return `借贷合计金额不平衡，请检查后重新保存`;
        }
        // 通过;
        return false;
    } else {
        return `第${currindex + 1}行数据，${tips}请检查数据填写完整后再提交`;
    }
};

// 这是新增摘要保存成功的时候列表要显示上去 所以此处模拟触发下change事件
const listenmitt = (data: any) => {
    selectChange(data, 'abstract');
    // emits('changeSelectValue', data, 'abstract', currtIndex);
};
// 新增的科目通知到这里直接填写上去
const listenSubjectMitt = (data: any) => {
    selectChange(data, 'subject');
};
const set_quick_edit = (data: any) => {
    console.log('shun22', data);
    // 第一步先清空数据
    clearTableData();
    // 赋值 第一行
    if (data.debitSubjectData) {
        currtIndex = 0;
        selectChange({ text: data.serverType.summary }, 'abstract'); // 设置摘要
        selectChange(data.debitSubjectData, 'subject'); // 设置科目
    }
    if (data.creditSubjectData) {
        currtIndex = 1;
        selectChange({ text: data.serverType.summary }, 'abstract'); // 设置摘要
        selectChange(data.creditSubjectData, 'subject'); // 设置科目
    }
    if (data.taxSubjectData) {
        currtIndex = 2;
        selectChange({ text: data.serverType.summary }, 'abstract'); // 设置摘要
        selectChange(data.taxSubjectData, 'subject'); // 设置科目
    }
    // debitSubjectData
};
onMounted(() => {
    document.addEventListener('click', selectshow);
    emitter.on('account_voucher_newly_added', listenmitt);
    emitter.on('account_voucher_subject_added', listenSubjectMitt);
    emitter.on('quick_edit_set_voucher', set_quick_edit);
});
onUnmounted(() => {
    document.removeEventListener('click', selectshow);
    emitter.off('account_voucher_newly_added', listenmitt);
    emitter.off('account_voucher_subject_added', listenSubjectMitt);
    emitter.off('quick_edit_set_voucher', set_quick_edit);
});
const getTargetXY = (DOM: any, index: number) => {
    return {
        x: DOM.offsetLeft,
        y: 60 + index * 68,
        w: DOM.offsetWidth,
        h: DOM.offsetHeight,
    };
};
const getAppointDom = (dom: any, classname: string) => {
    let nodeDom = dom;
    let index = 0;
    while (!nodeDom.className?.includes(classname) && index < 30) {
        nodeDom = nodeDom.parentNode;
        index++;
    }
    if (index === 30) {
        console.error('调用getAppointDom 出错没有找到元素');
    }
    return nodeDom;
};
/**
 * 选项区域点击事件
 */
const selectAreClick = (e: Event, type: string, index: number) => {
    currtIndex = index;
    e.preventDefault();
    e.stopPropagation();
    if (type === 'abstract') {
        const data = getTargetXY(e.target, index);
        abstractCoor.value = data;
        subjectCoor.value = undefined;
        inputCoor.value = undefined;
        currencyCoor.value = undefined;
        // 获取摘要数据
        if (useAbstract.selectdata.value.length === 0) {
            // 获取数据
            useAbstract.fetchData();
        }
    } else if (type === 'subject') {
        const Dom = getAppointDom(e.target, 'subjectcont');
        const data = getTargetXY(Dom, index);
        abstractCoor.value = undefined;
        inputCoor.value = undefined;
        currencyCoor.value = undefined;
        subjectCoor.value = data;

        // 获取科目数据
        if (useSubject.selectdata.value.length === 0) {
            // 获取数据
            useSubject.fetchData();
        }
    }
};
// 金额输入区域点击事件
const inputAreClick = (e: any, type: string, index: number) => {
    e.preventDefault();
    e.stopPropagation();
    inputType = type;
    currtIndex = index;
    console.log(e, type, index);
    const data = getTargetXY(e.target, index);
    inputCoor.value = data;
    abstractCoor.value = undefined;
    subjectCoor.value = undefined;
    currencyCoor.value = undefined;
    if (type === 'borrower') {
        inputValue.value = state.list[index]?.borrower as string;
    } else if (type === 'lender') {
        inputValue.value = state.list[index]?.lender as string;
    }
    nextTick(() => {
        inputRef.value.focus();
        inputRef.value.select();
    });
};
// 外币输入区域点击事件
const foreignInputClick = (e: any, index: number) => {
    e.preventDefault();
    e.stopPropagation();
    currtIndex = index;
    const data = getTargetXY(e.target, index);
    currencyCoor.value = data;
    inputCoor.value = undefined;
    abstractCoor.value = undefined;
    subjectCoor.value = undefined;
    foreignMoney.value = state.list[index]?.currency?.money as string; // 外币金额
    foreignRate.value = state.list[index]?.currency?.rate as string; // 外币汇率
    foreignType.value = state.list[index]?.currency?.currencyCode as string; // 外币种类
};
const inputblur = () => {
    const itm = state.list[currtIndex];
    if (itm) {
        if (inputType === 'borrower' && itm.borrower != inputValue.value) {
            // 借方
            itm.borrower = inputValue.value;
            itm.lender = '';
            // 进行余额计算
            if (itm.subject) {
                itm.balance = Number(itm.subject.balance) + Number(itm.borrower);
            }
        } else if (inputType === 'lender' && itm.lender != inputValue.value) {
            // 贷方
            itm.lender = inputValue.value;
            itm.borrower = '';
            if (itm.subject) {
                itm.balance = itm.subject.balance - Number(itm.lender);
            }
        }
        // TODO 当有外币的时候计算比较特殊
    }
    // emits('changeInputValue', inputValue.value, inputType, currtIndex);
};
const selectChange = (data: any, type: string) => {
    const itm = state.list[currtIndex];
    if (itm) {
        if (type === 'abstract') {
            // 摘要设置
            itm.abstract = data;
        } else if (type === 'subject') {
            // data.types = [
            //     {
            //         text: '供应商',
            //         type: 'Supplier',
            //     },
            //     {
            //         text: '项目',
            //         type: 'Project',
            //     },
            //     {
            //         text: '部门',
            //         type: 'Department',
            //     },
            //     {
            //         text: '存货',
            //         type: 'Stock',
            //     },
            // ];
            // 科目发生变化
            itm.subject = data;
            itm.balance = data.balance;
            // 科目发生变化
            // 做特殊处理
            // 判断科目里是否有外币
            // data.isForCurrency = true;
            // data.currencyCode = 'USE';
            if (data.isForCurrency) {
                // 有外币核算
                itm.currency = {
                    currencyCode: data.currencyCode,
                    money: '',
                    rate: '',
                };
            } else {
                delete itm.currency;
            }
        }
    }
    // emits('changeSelectValue', itm, type, currtIndex);
};
const handleclick = (type: string, index: number) => {
    if (type === 'add') {
        // 添加 在当前行的下面添加
        state.list.splice(index + 1, 0, {
            borrower: '',
            lender: '',
            balance: 0,
        });
    } else if (type === 'delete') {
        // 删除
        // 如果多余4个直接删除当前行
        // 小于4个是清空当前行的数据
        if (state.list.length > 4) {
            state.list.splice(index, 1);
        } else {
            if (state.list[index]) {
                state.list[index].abstract = undefined;
                state.list[index].subject = undefined;
                state.list[index].borrower = '';
                state.list[index].lender = '';
                delete state.list[index].currency;
            }
        }
    }
    // mits('addOrDeleteTable', type, index);
};
// 外币数量输入失去焦点事件
const foreignMoneyBlur = () => {
    const itm = state.list[currtIndex];
    if (itm && itm.currency) {
        itm.currency.money = foreignMoney.value;
    }
    // emits('foreignChange', 'money', foreignMoney.value, currtIndex);
};
// 外币数量输入失去焦点事件
const foreignMoneyRate = () => {
    const itm = state.list[currtIndex];
    if (itm && itm.currency) {
        itm.currency.rate = foreignRate.value;
    }
    // emits('foreignChange', 'rate', foreignRate.value, currtIndex);
};
const foreignInputOutClick = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
};
// 新增摘要或者科目
const newlyAddedClick = (type: string) => {
    console.log(type);
    if (type === 'abstract') {
        SummarymodalApi.open();
    } else if (type === 'subject') {
        SubjectmodalApi.open();
    }
};
const processingDataFilled = (newval: any) => {
    // 对借方贷方求和
    let borrower = 0;
    let lender = 0;
    let is_foreign_currency = false;
    newval.map((v: any) => {
        borrower += Number(v.borrower);
        lender += Number(v.lender);
        if (v.currency) {
            is_foreign_currency = true;
        }
        return true;
    });
    state.borrowerAll = `${borrower}`;
    state.lenderAll = `${lender}`;
    if (state.borrowerAll == state.lenderAll && Number(state.borrowerAll) > 0) {
        state.amountTo = uTcurrencyToUpperCase(Number(state.borrowerAll));
    } else {
        state.amountTo = '';
    }
    state.isCurrency = is_foreign_currency; // 是否有外币
    // 对数据进行本地存储便于编写
    localStorage.setItem(`${import.meta.env.VITE_APP_NAMESPACE}-current-voucher`, JSON.stringify(newval));
    localStorage.setItem(`${import.meta.env.VITE_APP_NAMESPACE}-current-voucher-customer_id`, useCustomer.customerId);
};
watch(
    state.list,
    (newval) => {
        processingDataFilled(newval);
    },
    { flush: 'post' },
);
// 清空表格数据
const clearTableData = () => {
    state.list.map((v) => {
        v.abstract = undefined;
        v.subject = undefined;
        v.borrower = '';
        v.lender = '';
        delete v.currency;
    });
    state.borrowerAll = '';
    state.lenderAll = '';
    state.amountTo = '';
};
// 组件暴露给父组件的方法
defineExpose({
    data_validation,
    state,
    clearTableData,
});
</script>
<template>
    <div
        class="add-voucher-tablebox"
        ref="outDomRef"
    >
        <div>
            <ul class="headul">
                <li class="li-abstract">摘要</li>
                <li class="li-subject">会计科目</li>
                <li
                    v-if="state.isCurrency"
                    class="li-currency"
                >
                    外币核算
                </li>
                <li class="li2">
                    <ul class="tit">
                        <li>借方金额</li>
                    </ul>
                    <Ulvalue
                        type="head"
                        classname="moneyul"
                    />
                </li>
                <li class="li2">
                    <ul class="tit">
                        <li>贷方金额</li>
                    </ul>
                    <Ulvalue
                        type="head"
                        classname="moneyul"
                    />
                </li>
            </ul>
            <ul
                v-for="(itm, i) in state.list"
                :key="i"
                class="bodyul"
            >
                <div class="connect"></div>
                <div class="add">
                    <PlusOutlined
                        @click="
                            () => {
                                handleclick('add', i);
                            }
                        "
                    />
                </div>
                <li
                    @click="
                        (e) => {
                            selectAreClick(e, 'abstract', i);
                        }
                    "
                    class="li-abstract"
                >
                    <div class="d1 ml-2">
                        {{ itm.abstract?.text }}
                    </div>
                </li>
                <li
                    @click="
                        (e) => {
                            selectAreClick(e, 'subject', i);
                        }
                    "
                    class="li-subject subjectcont"
                >
                    <div
                        v-if="itm.subject"
                        class="subjectbox ml-2"
                    >
                        <div>{{ itm.subject?.text }}</div>
                        <template v-if="itm.subject.types">
                            <div
                                @click.stop
                                class="supplier"
                                v-for="(vv, ii) in itm.subject.types"
                                :key="ii"
                            >
                                <div>{{ vv.text }}：</div>
                                <div>
                                    <AuxiliarySelect :type="vv.type" />
                                </div>
                            </div>
                        </template>

                        <div :class="[itm.balance < 0 ? 'text-red-500' : '']">余额：{{ itm.balance }}</div>
                    </div>
                </li>
                <li
                    v-if="state.isCurrency"
                    class="li-currency"
                >
                    <div
                        class="currencybox"
                        v-if="itm.currency"
                        @click="
                            (e) => {
                                foreignInputClick(e, i);
                            }
                        "
                    >
                        <div>{{ itm.currency.currencyCode }}：{{ itm.currency.money }}</div>
                        <div>汇率：{{ itm.currency.rate }}</div>
                    </div>
                </li>
                <li
                    class="li2"
                    @click="
                        (e) => {
                            inputAreClick(e, 'borrower', i);
                        }
                    "
                >
                    <Ulvalue
                        classname="moneyul"
                        :value="uTconvert(itm.borrower, '元')"
                    />
                </li>
                <li
                    class="li2"
                    @click="
                        (e) => {
                            inputAreClick(e, 'lender', i);
                        }
                    "
                >
                    <Ulvalue
                        classname="moneyul"
                        :value="uTconvert(itm.lender, '元')"
                    />
                </li>
                <div class="connect-remove"></div>
                <div class="remove">
                    <MinusOutlined
                        @click="
                            () => {
                                handleclick('delete', i);
                            }
                        "
                    />
                </div>
            </ul>
            <ul class="footerul">
                <li
                    class="li3"
                    :class="[state.isCurrency ? 'li3-currency' : '']"
                >
                    <div>合计：{{ state.amountTo }}</div>
                </li>
                <li class="li2">
                    <Ulvalue
                        classname="moneyul"
                        :value="uTconvert(state.borrowerAll, '元')"
                    />
                </li>
                <li class="li2">
                    <Ulvalue
                        classname="moneyul"
                        :value="uTconvert(state.lenderAll, '元')"
                    />
                </li>
            </ul>
            <div
                class="selectbox"
                v-if="abstractCoor"
                :style="{
                    width: `${abstractCoor.w}px`,
                    left: `${abstractCoor.x}px`,
                    top: `${abstractCoor.y + 60}px`,
                }"
            >
                <UserSelectBox
                    :options="useAbstract.selectdata"
                    type="abstract"
                    btntext="新增摘要"
                    @select-change="selectChange"
                    @newly-added-click="
                        () => {
                            newlyAddedClick('abstract');
                        }
                    "
                />
            </div>
            <div
                class="selectbox"
                v-if="subjectCoor"
                :style="{
                    width: `${subjectCoor.w}px`,
                    left: `${subjectCoor.x}px`,
                    top: `${subjectCoor.y + 60}px`,
                }"
            >
                <UserSelectBox
                    :options="useSubject.selectdata"
                    type="subject"
                    btntext="新增科目"
                    @select-change="selectChange"
                    @newly-added-click="
                        () => {
                            newlyAddedClick('subject');
                        }
                    "
                />
            </div>
            <div
                class="inputbox"
                v-if="inputCoor"
                @click.stop
                :style="{
                    width: `${inputCoor.w}px`,
                    height: `${inputCoor.h}px`,
                    left: `${inputCoor.x}px`,
                    top: `${inputCoor.y}px`,
                }"
            >
                <a-input
                    style="width: 100%; height: 100%"
                    v-model:value="inputValue"
                    ref="inputRef"
                    type="number"
                    placeholder=""
                    class="text-right"
                    @blur="inputblur"
                />
            </div>
            <div
                class="exchange-input"
                v-if="currencyCoor"
                :style="{
                    left: `${currencyCoor.x + 100}px`,
                    top: `${currencyCoor.y}px`,
                }"
                @click="foreignInputOutClick"
            >
                <div>
                    {{ foreignType }}
                    <a-input
                        style="width: 100px"
                        v-model:value="foreignMoney"
                        type="number"
                        ref="inputRef"
                        placeholder=""
                        class="text-right"
                        @blur="foreignMoneyBlur"
                    />
                </div>
                <div class="mt-2">
                    汇率
                    <a-input
                        style="width: 100px"
                        v-model:value="foreignRate"
                        ref="inputRef"
                        placeholder=""
                        class="text-right"
                        @blur="foreignMoneyRate"
                    />
                </div>
            </div>
        </div>

        <SummaryModal class="w-[500px]" />
        <SubjectModal
            type=""
            class="w-[800px]"
        />
    </div>
</template>
<style lang="scss">
.add-voucher-tablebox {
    position: relative;
    width: 97%;
    height: auto;

    /* width: 100%; */
    margin: 0 auto;
    font-size: 0;

    & > div {
        border: solid 1px rgb(217 218 220);
        border-right: none;
        border-bottom: none;
    }

    .selectbox {
        position: absolute;
        height: 300px;
    }

    /* 科目样式处理 */
    .subjectcont {
        display: flex;
        align-items: center;

        .subjectbox {
            flex: 1;
            font-size: 12px;
            line-height: 20px;
            text-align: left;
        }

        .supplier {
            display: flex;
            flex-direction: row;
            width: 90%;

            & > div:nth-child(2) {
                flex: 1;
            }
        }
    }

    .inputbox {
        position: absolute;
    }

    li {
        display: inline-block;
        vertical-align: top;
        border-right: solid 1px rgb(217 218 220);
        border-bottom: solid 1px rgb(217 218 220);
    }

    ul {
        display: flex;

        /* flex-direction: row; */

        li {
            flex: 1;
            font-size: 14px;
            color: rgb(51 51 51);
        }
    }

    .li2 {
        flex: 0.7;
        border-right: none;
        border-bottom: none;
    }

    .headul {
        height: 60px;
        font-weight: 500;
        line-height: 60px;
        text-align: center;
        background-color: rgb(245 245 245);

        .li2 ul {
            height: 30px;
            line-height: 30px;
        }

        .moneyul li {
            font-size: 12px;
        }
    }

    .li-abstract {
        flex: 0.5;
    }

    .li-subject {
        flex: 1.5;
    }

    .li-currency {
        flex: 0.5;
        line-height: 60px;

        .currencybox {
            display: inline-block;
            width: 85%;
            margin-left: 10px;
            font-size: 12px;
            line-height: 26px;
            color: #999;
            vertical-align: middle;

            div {
                pointer-events: none;
            }
        }
    }

    .bodyul {
        position: relative;

        .connect,
        .connect-remove {
            position: absolute;
            top: 0;
            left: -20px;
            width: 20px;
            height: 68px;
        }

        .connect-remove {
            right: -20px;
            left: auto;
        }

        &:hover {
            .connect,
            .connect-remove {
                display: block;
            }

            .add,
            .remove {
                display: block;
                cursor: pointer;
            }
        }

        .add,
        .remove {
            position: absolute;
            top: 25px;
            left: -20px;
            z-index: 1;
            display: none;
            width: 18px;
            height: 18px;
            font-size: 12px;
            line-height: 12px;
            text-align: center;
            border-style: solid;
            border-width: 2px;
            border-radius: 50%;
        }

        .add {
            color: rgb(22 119 255 / 100%);
            border-color: rgb(22 119 255 / 100%);
        }

        .remove {
            right: -20px;
            left: auto;
            color: rgb(245 108 108 / 100%);
            border-color: rgb(245 108 108 / 100%);
        }

        li {
            /* height: 100%; */
            min-height: 68px;
            font-size: 14px;
            line-height: 68px;
        }
    }

    .footerul {
        background-color: rgb(254 251 242);

        li {
            height: 68px;
            line-height: 68px;
        }

        .li3 {
            flex: 2;
            text-align: left;

            div {
                margin-left: 20px;
            }
        }

        .li3-currency {
            flex: 2.51;
        }
    }

    .moneyul {
        /* flex-direction: row-reverse; */
        height: 100%;
        font-weight: 500;

        li {
            text-align: center;
        }

        li:nth-child(4) {
            border-right: solid 1px rgb(170 197 234);
        }

        li:nth-child(7) {
            border-right: solid 1px rgb(170 197 234);
        }

        li:nth-child(10) {
            border-right: solid 1px rgb(229 168 165);
        }
    }

    .exchange-input {
        position: absolute;
        z-index: 1;
        width: 160px;
        height: 100px;
        padding: 10px;
        font-size: 12px;
        background-color: #fff;
        border: solid 1px #ccc;
        border-radius: 5px;
    }
}
</style>
