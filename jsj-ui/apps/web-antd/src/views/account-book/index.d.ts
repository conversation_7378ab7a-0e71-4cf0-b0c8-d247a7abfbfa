// 记账凭证相关类型定义

export interface ListItm {
  id: string;
  abstract?: {
    id: string;
    text: string;
  };
  subject?: {
    id: string;
    name: string;
    code: string;
    text: string;
  };
  borrower?: string; // 借方金额
  lender?: string;   // 贷方金额
  auxiliary?: {
    id: string;
    name: string;
  };
}

export interface VoucherState {
  list: ListItm[];
  borrowerAll: string;
  lenderAll: string;
}
