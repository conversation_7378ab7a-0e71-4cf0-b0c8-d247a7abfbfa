// 工具函数集合

// 把指定数字转换成指定位数的字符串
export const uTpadNumber = (num: number, digits: number): string => {
  // 将数字转换为字符串
  const numStr = num.toString();

  // 计算需要补零的个数
  const zerosToAdd = digits - numStr.length;

  // 如果需要补零的个数大于0，则在前面补零
  if (zerosToAdd > 0) {
    return '0'.repeat(zerosToAdd) + numStr;
  }

  // 如果数字本身的位数已经等于或超过要求的位数，直接返回字符串形式
  return numStr;
};

// 把数字或者字符串转换成指定位数的字符串倒叙排列
export const uTconvert = (value: number | string, type: string): string => {
  if (!value) {
    return '';
  }
  const str = type === '元' ? ((value as number) * 100).toString() : value.toString();
  return [...str].reverse().join('');
};

// 数字金额转中文大写金额
export const uTcurrencyToUpperCase = (money: any): string => {
  if (money && !Number.isNaN(Number.parseFloat(money)) && Number.isFinite(money)) {
    // 默认值设置为"零元整"，如果输入为空或非法，则返回这个默认值
    let cnMoney = '零元整';
    // 用于构建输出字符串的变量
    let strOutput = '';
    // 中文单位数组，从高到低排列
    let strUnit = '仟佰拾亿仟佰拾万仟佰拾元角分';
    // 定义数字位数字符串
    const numCapitalLetters = '零壹贰叁肆伍陆柒捌玖';
    // 将传入的金额字符串后添加两个零，确保小数部分存在
    money += '00';
    // 查找小数点的位置
    const intPos = money.indexOf('.');
    if (intPos !== -1) {
      // 如果存在小数点，则移除小数点，并保留前两位作为小数部分
      money = money.slice(0, Math.max(0, intPos)) + money.substr(intPos + 1, 2);
    }
    // 长度限制
    if ((strUnit.length - money.length) < 0) return '长度超出限制，最大支持千亿位';
    // 根据money的长度裁剪strUnit，以匹配money中每个数字对应的中文单位
    strUnit = strUnit.substr(strUnit.length - money.length);
    // 遍历money中的每一个字符
    for (var i = 0; i < money.length; i++) {
      // 对应数字转换为中文大写，并加上相应的中文单位
      strOutput += numCapitalLetters.substr(money.substr(i, 1), 1) + strUnit.substr(i, 1);
    }
    // 使用正则表达式处理中文大写金额字符串，进行格式化
    cnMoney = strOutput
      .replace(/零角零分$/, '整') // 如果最后是"零角零分"，替换为"整"
      .replace(/零[仟佰拾]/g, '零') // 移除多余的零（如"零仟"、"零佰"等）
      .replace(/零{2,}/g, '零') // 连续多个零只保留一个
      .replace(/零([亿|万])/g, '$1') // 去掉亿、万前面的零
      .replace(/零+元/, '元') // 去掉元前面的所有零
      .replace(/亿零{0,3}万/, '亿') // 如果亿后面有零和万，只保留亿
      .replace(/^元/, '零元'); // 如果最开始就是元，添加"零"

    // 返回最终处理后的中文大写金额字符串
    return cnMoney;
  } else {
    return '非法参数或参数不存在';
  }
};

export const uTgetTime = (date?: any): [string, string, string, string, string, string] => {
  const d = date || new Date();
  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  const h = d.getHours();
  const m = d.getMinutes();
  const s = d.getSeconds();

  return [year, uTpadNumber(month, 2), uTpadNumber(day, 2), uTpadNumber(h, 2), uTpadNumber(m, 2), uTpadNumber(s, 2)];
};
