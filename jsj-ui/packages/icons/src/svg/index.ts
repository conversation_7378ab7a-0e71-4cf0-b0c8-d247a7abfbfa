import { createIconifyIcon } from '@vben-core/icons';

import './load.js';

const SvgAvatar1Icon = createIconifyIcon('svg:avatar-1');
const SvgAvatar2Icon = createIconifyIcon('svg:avatar-2');
const SvgAvatar3Icon = createIconifyIcon('svg:avatar-3');
const SvgAvatar4Icon = createIconifyIcon('svg:avatar-4');
const SvgDownloadIcon = createIconifyIcon('svg:download');
const SvgCardIcon = createIconifyIcon('svg:card');
const SvgBellIcon = createIconifyIcon('svg:bell');
const SvgCakeIcon = createIconifyIcon('svg:cake');
const SvgAntdvLogoIcon = createIconifyIcon('svg:antdv-logo');
const SvgMaxKeyIcon = createIconifyIcon('svg:max-key');
const SvgTopiamIcon = createIconifyIcon('svg:topiam');
const SvgWechatIcon = createIconifyIcon('svg:wechat');
const SvgQQIcon = createIconifyIcon('svg:qq');
const SvgJisuanqi = createIconifyIcon('svg:user-jisuanqi');
const SvgAttention = createIconifyIcon('svg:attention');

export {
  SvgAntdvLogoIcon,
  SvgAttention,
  SvgAvatar1Icon,
  SvgAvatar2Icon,
  SvgAvatar3Icon,
  SvgAvatar4Icon,
  SvgBellIcon,
  SvgCakeIcon,
  SvgCardIcon,
  SvgDownloadIcon,
  SvgJisuanqi,
  SvgMaxKeyIcon,
  SvgQQIcon,
  SvgTopiamIcon,
  SvgWechatIcon,
};

export { default as SvgMessageUrl } from './icons/message.svg';
